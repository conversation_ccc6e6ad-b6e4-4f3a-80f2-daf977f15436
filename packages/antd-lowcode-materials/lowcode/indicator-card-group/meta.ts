import snippets from './snippets';

export default {
  snippets,
  componentName: 'IndicatorCardGroup',
  group: 'MelGeek组件',
  title: '指标卡片组',
  category: '驾驶舱',
  props: [
    {
      name: 'cards',
      title: { label: '卡片数据', tip: '指标卡片数据列表' },
      propType: 'array',
      setter: {
        componentName: 'ArraySetter',
        props: {
          itemSetter: {
            componentName: 'ObjectSetter',
            props: {
              config: {
                items: [
                  {
                    name: 'title',
                    title: '标题',
                    setter: 'StringSetter',
                  },
                  {
                    name: 'value',
                    title: '数值',
                    setter: 'NumberSetter',
                  },
                  {
                    name: 'precision',
                    title: '精度',
                    setter: 'NumberSetter',
                  },
                  {
                    name: 'prefix',
                    title: '前缀',
                    setter: 'StringSetter',
                  },
                  {
                    name: 'suffix',
                    title: '后缀',
                    setter: 'StringSetter',
                  },
                  {
                    name: 'trend',
                    title: '趋势',
                    setter: {
                      componentName: 'SelectSetter',
                      props: {
                        options: [
                          { title: '无', value: 'none' },
                          { title: '上升', value: 'up' },
                          { title: '下降', value: 'down' },
                        ],
                      },
                    },
                  },
                  {
                    name: 'trendValue',
                    title: '趋势值',
                    setter: 'StringSetter',
                  },
                  {
                    name: 'loading',
                    title: '加载中',
                    setter: 'BoolSetter',
                  },
                ],
              },
            },
            initialValue: () => {
              return {
                title: '指标名称',
                value: 100,
                trend: 'none',
              };
            },
          },
        },
      },
    },
    {
      name: 'gutter',
      title: { label: '间距', tip: '卡片之间的间距' },
      propType: 'number',
      defaultValue: 16,
    },
    {
      name: 'span',
      title: { label: '列宽', tip: '每个卡片占用的列数（总共24列）' },
      propType: 'number',
      defaultValue: 6,
    },
  ],
  configure: {
    props: [
      {
        name: 'cards',
        title: {
          label: {
            type: 'i18n',
            zh_CN: '卡片数据',
            en_US: 'Cards Data',
          },
          tip: {
            type: 'i18n',
            zh_CN: '属性: cards | 说明: 指标卡片数据列表',
            en_US: 'prop: cards | description: Indicator cards data list',
          },
        },
        setter: {
          componentName: 'ArraySetter',
          props: {
            itemSetter: {
              componentName: 'ObjectSetter',
              props: {
                config: {
                  items: [
                    {
                      name: 'title',
                      title: '标题',
                      setter: 'StringSetter',
                      isRequired: true
                    },
                    {
                      name: 'value',
                      title: '数值',
                      setter: 'NumberSetter',
                      isRequired: true
                    },
                    {
                      name: 'precision',
                      title: '精度',
                      setter: 'NumberSetter',
                    },
                    {
                      name: 'prefix',
                      title: '前缀',
                      setter: 'StringSetter',
                    },
                    {
                      name: 'suffix',
                      title: '后缀',
                      setter: 'StringSetter',
                    },
                    {
                      name: 'trend',
                      title: '趋势',
                      setter: {
                        componentName: 'SelectSetter',
                        props: {
                          options: [
                            { title: '无', value: 'none' },
                            { title: '上升', value: 'up' },
                            { title: '下降', value: 'down' },
                          ],
                        },
                      },
                    },
                    {
                      name: 'trendValue',
                      title: '趋势值',
                      setter: 'StringSetter',
                    },
                    {
                      name: 'loading',
                      title: '加载中',
                      setter: 'BoolSetter',
                    },
                  ],
                },
              },
              initialValue: () => {
                return {
                  title: '指标名称',
                  value: 100,
                  trend: 'none',
                };
              },
            },
          },
        },
        supportVariable: true,
      },
      {
        name: 'gutter',
        title: {
          label: {
            type: 'i18n',
            zh_CN: '间距',
            en_US: 'Gutter',
          },
          tip: {
            type: 'i18n',
            zh_CN: '属性: gutter | 说明：卡片之间的间距',
            en_US: 'prop: gutter | description: Spacing between cards',
          }
        },
        setter: 'NumberSetter',
        defaultValue: 16,
        supportVariable: true,
      },
      {
        name: 'span',
        title: {
          label: {
            type: 'i18n',
            zh_CN: '列宽',
            en_US: 'Span',
          },
          tip: {
            type: 'i18n',
            zh_CN: '属性: span | 说明：每个卡片占用的列数（总共24列）',
            en_US: 'prop: span | description: Number of columns each card occupies (total 24 columns)',
          }
        },
        setter: 'NumberSetter',
        defaultValue: 6,
        supportVariable: true,
      },
    ],
    supports: {
      style: true,
      className: true,
    },
  },
};
