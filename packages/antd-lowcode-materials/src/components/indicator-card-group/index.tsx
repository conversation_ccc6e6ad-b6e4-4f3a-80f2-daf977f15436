import React from 'react';
import { Card, Statistic, Row, Col } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';

import './index.scss';

interface IndicatorCardProps {
  title?: string;
  value?: number | string;
  precision?: number;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  trend?: 'up' | 'down' | 'none';
  trendValue?: string;
  yearOverYear?: {
    value: string;
    trend: 'up' | 'down' | 'none';
  };
  monthOverMonth?: {
    value: string;
    trend: 'up' | 'down' | 'none';
  };
  loading?: boolean;
  style?: React.CSSProperties;
}

interface IndicatorCardGroupProps {
  cards?: IndicatorCardProps[];
  gutter?: number;
  span?: number;
  style?: React.CSSProperties;
  className?: string;
}

const IndicatorCard: React.FC<IndicatorCardProps> = ({
  title,
  value,
  precision,
  prefix,
  suffix,
  trend = 'none',
  trendValue,
  yearOverYear,
  monthOverMonth,
  loading = false,
  style,
}) => {
  const getTrendIcon = () => {
    if (trend === 'up') return <ArrowUpOutlined style={{ color: '#3f8600' }} />;
    if (trend === 'down') return <ArrowDownOutlined style={{ color: '#cf1322' }} />;
    return null;
  };

  const getTrendColor = () => {
    if (trend === 'up') return '#3f8600';
    if (trend === 'down') return '#cf1322';
    return undefined;
  };

  const renderTrendItem = (label: string, data?: { value: string; trend: 'up' | 'down' | 'none' }) => {
    if (!data || !data.value) return null;

    const trendIcon = data.trend === 'up' ? <ArrowUpOutlined style={{ color: '#3f8600' }} /> :
                     data.trend === 'down' ? <ArrowDownOutlined style={{ color: '#cf1322' }} /> : null;
    const trendColor = data.trend === 'up' ? '#3f8600' : data.trend === 'down' ? '#cf1322' : '#666';

    return (
      <div style={{ fontSize: '12px', color: trendColor, marginTop: 4 }}>
        {label}：{trendIcon && <span style={{ marginRight: 2 }}>{trendIcon}</span>}{data.value}
      </div>
    );
  };

  return (
    <Card loading={loading} style={style}>
      <Statistic
        title={title}
        value={value}
        precision={precision}
        prefix={prefix}
        suffix={suffix}
        valueStyle={{ color: getTrendColor() }}
      />
      {trend !== 'none' && trendValue && (
        <div
          style={{
            marginTop: 8,
            fontSize: '14px',
            color: getTrendColor(),
          }}
        >
          {getTrendIcon()}
          <span style={{ marginLeft: 4 }}>{trendValue}</span>
        </div>
      )}
      {(yearOverYear || monthOverMonth) && (
        <div style={{ marginTop: 8 }}>
          {renderTrendItem('同比', yearOverYear)}
          {renderTrendItem('环比', monthOverMonth)}
        </div>
      )}
    </Card>
  );
};

const IndicatorCardGroup: React.FC<IndicatorCardGroupProps> = ({
  cards = [],
  gutter = 16,
  span = 6,
  style,
  className,
}) => {
  return (
    <div className={className} style={style}>
      <Row gutter={gutter}>
        {cards.map((card, index) => (
          <Col span={span} key={index}>
            <IndicatorCard {...card} />
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default IndicatorCardGroup;
export { IndicatorCardGroup };