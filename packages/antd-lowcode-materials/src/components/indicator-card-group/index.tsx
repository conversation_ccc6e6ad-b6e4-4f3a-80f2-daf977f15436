import React from 'react';
import { Card, Statistic, Row, Col } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';

interface IndicatorCardProps {
  title?: string;
  value?: number | string;
  precision?: number;
  prefix?: React.ReactNode;
  suffix?: React.ReactNode;
  trend?: 'up' | 'down' | 'none';
  trendValue?: string;
  loading?: boolean;
  style?: React.CSSProperties;
}

interface IndicatorCardGroupProps {
  cards?: IndicatorCardProps[];
  gutter?: number;
  span?: number;
  style?: React.CSSProperties;
  className?: string;
}

const IndicatorCard: React.FC<IndicatorCardProps> = ({
  title,
  value,
  precision,
  prefix,
  suffix,
  trend = 'none',
  trendValue,
  loading = false,
  style,
}) => {
  const getTrendIcon = () => {
    if (trend === 'up') return <ArrowUpOutlined style={{ color: '#3f8600' }} />;
    if (trend === 'down') return <ArrowDownOutlined style={{ color: '#cf1322' }} />;
    return null;
  };

  const getTrendColor = () => {
    if (trend === 'up') return '#3f8600';
    if (trend === 'down') return '#cf1322';
    return undefined;
  };

  return (
    <Card loading={loading} style={style}>
      <Statistic
        title={title}
        value={value}
        precision={precision}
        prefix={prefix}
        suffix={suffix}
        valueStyle={{ color: getTrendColor() }}
      />
      {trend !== 'none' && trendValue && (
        <div
          style={{
            marginTop: 8,
            fontSize: '14px',
            color: getTrendColor(),
          }}
        >
          {getTrendIcon()}
          <span style={{ marginLeft: 4 }}>{trendValue}</span>
        </div>
      )}
    </Card>
  );
};

const IndicatorCardGroup: React.FC<IndicatorCardGroupProps> = ({
  cards = [],
  gutter = 16,
  span = 6,
  style,
  className,
}) => {
  return (
    <div className={className} style={style}>
      <Row gutter={gutter}>
        {cards.map((card, index) => (
          <Col span={span} key={index}>
            <IndicatorCard {...card} />
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default IndicatorCardGroup;
export { IndicatorCardGroup };